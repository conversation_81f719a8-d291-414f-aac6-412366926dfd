<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - AI Image Generator</title>
  <link rel="stylesheet" href="/styles.css?v=<%= Date.now() %>" onload="console.log('CSS loaded successfully')" onerror="console.error('CSS failed to load')">
  <!-- Fallback styles in case main CSS fails -->
  <style>
    body { font-family: system-ui, -apple-system, sans-serif; margin: 0; padding: 0; }
    .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
    .header { background: #fff; padding: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
  </style>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
  <meta name="theme-color" content="#6366f1">
  <meta name="description" content="AI Image Generator - Create stunning images with GPT-Image-1">
</head>
<body>
  <header class="header">
    <div class="container">
      <div class="header-content">
        <a href="/" class="logo">🤖 AI Image Generator</a>
        <nav class="nav">
          <a href="/" class="nav-link active">Dashboard</a>
          <a href="/add" class="nav-link">Add Prompt</a>
          <a href="/history" class="nav-link">History</a>
          <a href="/settings" class="nav-link">Settings</a>
        </nav>
      </div>
    </div>
  </header>

  <main class="container">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <h1 class="dashboard-title">
        <span class="dashboard-icon">🤖</span>
        AI Image Generator Dashboard
      </h1>
      <p class="dashboard-description">
        Manage your prompts and monitor AI image generation activity
      </p>
    </div>

<!-- Enhanced Stats Cards -->
<div class="stats enhanced">
  <div class="stat-card primary">
    <div class="stat-icon">📝</div>
    <div class="stat-content">
      <div class="stat-number"><%= prompts.length %></div>
      <div class="stat-label">Total Prompts</div>
    </div>
    <div class="stat-trend">
      <span class="trend-indicator positive">↗</span>
    </div>
  </div>

  <div class="stat-card success">
    <div class="stat-icon">⚡</div>
    <div class="stat-content">
      <div class="stat-number"><%= prompts.filter(p => p.enabled).length %></div>
      <div class="stat-label">Active Prompts</div>
    </div>
    <div class="stat-trend">
      <span class="trend-indicator positive">↗</span>
    </div>
  </div>

  <div class="stat-card info">
    <div class="stat-icon">🎨</div>
    <div class="stat-content">
      <div class="stat-number"><%= settings.totalGenerations || 0 %></div>
      <div class="stat-label">Total Generations</div>
    </div>
    <div class="stat-trend">
      <span class="trend-indicator positive">↗</span>
    </div>
  </div>

  <div class="stat-card warning">
    <div class="stat-icon">📊</div>
    <div class="stat-content">
      <div class="stat-number"><%= generationHistory.length %></div>
      <div class="stat-label">Recent Activity</div>
    </div>
    <div class="stat-trend">
      <span class="trend-indicator neutral">→</span>
    </div>
  </div>
</div>

<div class="grid grid-2">
  <!-- Prompts Section -->
  <div class="card">
    <div class="card-header">
      <h2 class="card-title">Prompts</h2>
      <a href="/add" class="btn btn-primary btn-sm">Add New</a>
    </div>
    
    <% if (prompts.length === 0) { %>
      <div class="text-center">
        <p class="text-secondary mb-2">No prompts yet</p>
        <a href="/add" class="btn btn-primary">Create Your First Prompt</a>
      </div>
    <% } else { %>
      <div class="grid grid-3">
        <% prompts.forEach(prompt => { %>
          <div class="prompt-card">
            <% if (prompt.referenceImage) { %>
              <img src="/uploads/<%= prompt.referenceImage %>" alt="Reference" class="prompt-image">
            <% } else { %>
              <div class="prompt-image">
                📄 No reference image
              </div>
            <% } %>
            
            <div class="prompt-content">
              <div class="prompt-text"><%= prompt.text %></div>
              
              <div class="prompt-meta">
                <span class="badge <%= prompt.enabled ? 'badge-success' : 'badge-danger' %>">
                  <%= prompt.enabled ? 'Enabled' : 'Disabled' %>
                </span>
                <span>Used <%= prompt.timesUsed || 0 %> times</span>
              </div>
              
              <div class="prompt-actions">
                <button onclick="togglePrompt('<%= prompt.id %>')" class="btn btn-sm btn-outline">
                  <%= prompt.enabled ? 'Disable' : 'Enable' %>
                </button>
                <button onclick="generateImage('<%= prompt.id %>')" class="btn btn-sm btn-secondary" 
                        <%= !prompt.enabled ? 'disabled' : '' %>>
                  Generate
                </button>
                <a href="/edit/<%= prompt.id %>" class="btn btn-sm btn-outline">Edit</a>
                <button onclick="deletePrompt('<%= prompt.id %>')" class="btn btn-sm btn-danger">Delete</button>
              </div>
            </div>
          </div>
        <% }); %>
      </div>
    <% } %>
  </div>

  <!-- Recent Activity -->
  <div class="card">
    <div class="card-header">
      <h2 class="card-title">Recent Generations</h2>
      <a href="/history" class="btn btn-outline btn-sm">View All</a>
    </div>
    
    <% if (generationHistory.length === 0) { %>
      <div class="text-center">
        <p class="text-secondary">No generations yet</p>
      </div>
    <% } else { %>
      <% generationHistory.forEach(item => { %>
        <div class="history-item">
          <% if (item.cloudinaryUrl) { %>
            <img src="<%= item.cloudinaryUrl %>" alt="Generated" class="history-image">
          <% } else { %>
            <div class="history-image">🖼️</div>
          <% } %>
          
          <div class="history-content">
            <div class="history-prompt"><%= item.promptText %></div>
            <div class="history-meta">
              <%= new Date(item.generatedAt).toLocaleString() %> • 
              <span class="badge <%= item.manual ? 'badge-info' : 'badge-success' %>">
                <%= item.manual ? 'Manual' : 'Scheduled' %>
              </span>
            </div>
          </div>
          
          <% if (item.cloudinaryUrl) { %>
            <a href="<%= item.cloudinaryUrl %>" target="_blank" class="btn btn-sm btn-outline">View</a>
          <% } %>
        </div>
      <% }); %>
    <% } %>
  </div>
</div>

<!-- Status Messages -->
<div id="status-message" class="hidden"></div>

<script>
async function togglePrompt(id) {
  try {
    const response = await fetch(`/toggle/${id}`, { method: 'POST' });
    const result = await response.json();
    
    if (result.success) {
      location.reload();
    } else {
      showMessage('Failed to toggle prompt', 'error');
    }
  } catch (error) {
    showMessage('Error toggling prompt', 'error');
  }
}

async function generateImage(id) {
  const button = event.target;
  const originalText = button.textContent;
  
  button.disabled = true;
  button.innerHTML = '<div class="spinner"></div> Generating...';
  
  try {
    const response = await fetch(`/generate/${id}`, { method: 'POST' });
    const result = await response.json();
    
    if (result.success) {
      showMessage('Image generated successfully!', 'success');
      setTimeout(() => location.reload(), 1000);
    } else {
      showMessage(result.error || 'Generation failed', 'error');
    }
  } catch (error) {
    showMessage('Error generating image', 'error');
  } finally {
    button.disabled = false;
    button.textContent = originalText;
  }
}

async function deletePrompt(id) {
  if (!confirm('Are you sure you want to delete this prompt?')) return;
  
  try {
    const response = await fetch(`/delete/${id}`, { method: 'POST' });
    
    if (response.ok) {
      showMessage('Prompt deleted successfully', 'success');
      setTimeout(() => location.reload(), 1000);
    } else {
      showMessage('Failed to delete prompt', 'error');
    }
  } catch (error) {
    showMessage('Error deleting prompt', 'error');
  }
}

function showMessage(message, type) {
  const messageEl = document.getElementById('status-message');
  messageEl.className = `alert alert-${type}`;
  messageEl.textContent = message;
  messageEl.classList.remove('hidden');
  
  setTimeout(() => {
    messageEl.classList.add('hidden');
  }, 5000);
}
</script>

  </main>
  <script src="/script.js"></script>
</body>
</html>
