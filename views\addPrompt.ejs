<!-- Add Prompt Page with Beautiful Design -->
<div class="container-sm">
  <!-- <PERSON> Header -->
  <div class="page-header">
    <h1 class="page-title">
      <span class="page-icon">✨</span>
      Create New Prompts
    </h1>
    <p class="page-description">
      Add individual prompts or upload multiple prompts from a JSON file to
      generate stunning AI images
    </p>
  </div>

  <!-- Method Selection Tabs -->
  <div class="method-tabs">
    <button type="button" class="method-tab active" data-method="single">
      <span class="tab-icon">📝</span>
      Single Prompt
    </button>
    <button type="button" class="method-tab" data-method="bulk">
      <span class="tab-icon">📁</span>
      Bulk Upload (JSON)
    </button>
  </div>

  <!-- Single Prompt Form -->
  <div id="single-method" class="method-content active">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">
          <span>📝</span>
          Add Single Prompt
        </h2>
      </div>

      <form
        action="/add"
        method="POST"
        enctype="multipart/form-data"
        id="single-prompt-form"
      >
        <div class="form-group">
          <label for="text" class="form-label">
            <span class="label-icon">💭</span>
            Prompt Text *
          </label>
          <textarea
            id="text"
            name="text"
            class="form-textarea enhanced"
            placeholder="Describe the image you want to generate in detail..."
            required
            rows="4"
          ></textarea>
          <div class="form-help">
            <span class="help-icon">💡</span>
            Be specific and descriptive. This text will be combined with the
            reference image to generate AI art.
          </div>
        </div>

        <div class="form-group">
          <label for="referenceImage" class="form-label">
            <span class="label-icon">🖼️</span>
            Reference Image (Optional)
          </label>
          <div class="form-file enhanced" id="file-drop-zone">
            <input
              type="file"
              id="referenceImage"
              name="referenceImage"
              accept="image/*"
              onchange="previewImage(this)"
              hidden
            />
            <div class="file-upload-content">
              <div class="file-upload-icon">📸</div>
              <div class="file-upload-text">
                <strong>Click to select an image</strong> or drag and drop
              </div>
              <div class="file-upload-formats">
                Supports JPG, PNG, WebP formats (Max 10MB)
              </div>
            </div>
          </div>
        </div>

        <div id="image-preview" class="image-preview hidden">
          <div class="preview-header">
            <span class="preview-title">📸 Image Preview</span>
            <button type="button" class="btn-ghost btn-sm" onclick="removeImage()">
              <span>🗑️</span> Remove
            </button>
          </div>
          <img
            id="preview-img"
            src=""
            alt="Preview"
            class="preview-image"
          />
        </div>

        <div class="form-group">
          <div class="form-checkbox enhanced">
            <input type="checkbox" id="enabled" name="enabled" checked />
            <label for="enabled" class="checkbox-label">
              <span class="checkbox-icon">⚡</span>
              Enable this prompt for automatic generation
            </label>
          </div>
          <div class="form-help">
            <span class="help-icon">💡</span>
            When enabled, this prompt will be included in the automatic generation cycle.
          </div>
        </div>

        <div class="form-actions">
          <a href="/" class="btn btn-outline">
            <span>←</span> Cancel
          </a>
          <button type="submit" class="btn btn-primary">
            <span>✨</span> Add Prompt
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Bulk Upload Form -->
  <div id="bulk-method" class="method-content">
    <div class="card">
      <div class="card-header">
        <h2 class="card-title">
          <span>📁</span>
          Bulk Upload from JSON
        </h2>
      </div>

      <form action="/add-bulk" method="POST" enctype="multipart/form-data" id="bulk-upload-form">
        <div class="form-group">
          <label for="jsonFile" class="form-label">
            <span class="label-icon">📄</span>
            JSON File *
          </label>
          <div class="form-file enhanced" id="json-drop-zone">
            <input
              type="file"
              id="jsonFile"
              name="jsonFile"
              accept=".json"
              onchange="previewJSON(this)"
              hidden
            />
            <div class="file-upload-content">
              <div class="file-upload-icon">📄</div>
              <div class="file-upload-text">
                <strong>Click to select a JSON file</strong> or drag and drop
              </div>
              <div class="file-upload-formats">
                Only .json files are supported (Max 5MB)
              </div>
            </div>
          </div>
        </div>

        <div id="json-preview" class="json-preview hidden">
          <div class="preview-header">
            <span class="preview-title">📄 JSON Preview</span>
            <button type="button" class="btn-ghost btn-sm" onclick="removeJSON()">
              <span>🗑️</span> Remove
            </button>
          </div>
          <div class="json-stats">
            <div class="stat-item">
              <span class="stat-label">Prompts Found:</span>
              <span class="stat-value" id="prompts-count">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">File Size:</span>
              <span class="stat-value" id="file-size">0 KB</span>
            </div>
          </div>
          <pre id="json-content" class="json-content"></pre>
        </div>

        <div class="json-format-help">
          <div class="help-header">
            <h4>📋 Expected JSON Format:</h4>
            <a href="/sample-prompts.json" download="sample-prompts.json" class="btn btn-ghost btn-sm">
              <span>📥</span> Download Sample
            </a>
          </div>
          <pre class="json-example">{
  "prompts": [
    {
      "text": "A beautiful sunset over mountains",
      "enabled": true
    },
    {
      "text": "A futuristic city with flying cars",
      "enabled": true
    }
  ]
}</pre>
          <div class="format-notes">
            <p><strong>📝 Notes:</strong></p>
            <ul>
              <li>Each prompt must have a "text" field with the description</li>
              <li>The "enabled" field is optional (defaults to true)</li>
              <li>Reference images are not supported in bulk upload</li>
              <li>Maximum file size: 5MB</li>
            </ul>
          </div>
        </div>

        <div class="form-actions">
          <a href="/" class="btn btn-outline">
            <span>←</span> Cancel
          </a>
          <button type="submit" class="btn btn-secondary">
            <span>📁</span> Upload Prompts
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

    <script>
      // Tab switching functionality
      document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('.method-tab');
        const contents = document.querySelectorAll('.method-content');

        tabs.forEach(tab => {
          tab.addEventListener('click', function() {
            const method = this.dataset.method;

            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Update active content
            contents.forEach(c => c.classList.remove('active'));
            document.getElementById(method + '-method').classList.add('active');
          });
        });

        // Setup drag and drop for both file inputs
        setupDragAndDrop('file-drop-zone', 'referenceImage', previewImage);
        setupDragAndDrop('json-drop-zone', 'jsonFile', previewJSON);
      });

      // Image preview functionality
      function previewImage(input) {
        const preview = document.getElementById("image-preview");
        const previewImg = document.getElementById("preview-img");

        if (input.files && input.files[0]) {
          const reader = new FileReader();

          reader.onload = function (e) {
            previewImg.src = e.target.result;
            preview.classList.remove("hidden");
          };

          reader.readAsDataURL(input.files[0]);
        } else {
          preview.classList.add("hidden");
        }
      }

      function removeImage() {
        const input = document.getElementById("referenceImage");
        const preview = document.getElementById("image-preview");
        input.value = '';
        preview.classList.add("hidden");
      }

      // JSON preview functionality
      function previewJSON(input) {
        const preview = document.getElementById("json-preview");
        const content = document.getElementById("json-content");
        const promptsCount = document.getElementById("prompts-count");
        const fileSize = document.getElementById("file-size");

        if (input.files && input.files[0]) {
          const file = input.files[0];
          const reader = new FileReader();

          reader.onload = function (e) {
            try {
              const jsonData = JSON.parse(e.target.result);
              const prompts = jsonData.prompts || [];

              promptsCount.textContent = prompts.length;
              fileSize.textContent = (file.size / 1024).toFixed(1) + ' KB';
              content.textContent = JSON.stringify(jsonData, null, 2);
              preview.classList.remove("hidden");
            } catch (error) {
              alert('Invalid JSON file. Please check the format.');
              input.value = '';
            }
          };

          reader.readAsText(file);
        } else {
          preview.classList.add("hidden");
        }
      }

      function removeJSON() {
        const input = document.getElementById("jsonFile");
        const preview = document.getElementById("json-preview");
        input.value = '';
        preview.classList.add("hidden");
      }

      // Generic drag and drop setup
      function setupDragAndDrop(dropZoneId, inputId, previewFunction) {
        const dropZone = document.getElementById(dropZoneId);
        const input = document.getElementById(inputId);

        if (!dropZone || !input) return;

        dropZone.addEventListener("dragover", (e) => {
          e.preventDefault();
          dropZone.classList.add('drag-over');
        });

        dropZone.addEventListener("dragleave", (e) => {
          e.preventDefault();
          dropZone.classList.remove('drag-over');
        });

        dropZone.addEventListener("drop", (e) => {
          e.preventDefault();
          dropZone.classList.remove('drag-over');

          const files = e.dataTransfer.files;
          if (files.length > 0) {
            input.files = files;
            previewFunction(input);
          }
        });

        dropZone.addEventListener("click", () => {
          input.click();
        });
      }
    </script>
  </div>
</div>
