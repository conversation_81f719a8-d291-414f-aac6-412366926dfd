// Common JavaScript functionality for the AI Image Generator

// Set active navigation link based on current path
document.addEventListener('DOMContentLoaded', function() {
  const currentPath = window.location.pathname;
  const navLinks = document.querySelectorAll('.nav-link');

  navLinks.forEach(link => {
    if (link.getAttribute('href') === currentPath ||
        (currentPath === '/' && link.getAttribute('href') === '/')) {
      link.classList.add('active');
    }
  });

  // Handle success messages from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const successMessage = urlParams.get('success');

  if (successMessage) {
    showToast(successMessage, 'success');
    // Clean up URL without reloading
    const newUrl = window.location.pathname;
    window.history.replaceState({}, document.title, newUrl);
  }
});

// Utility function to show toast messages
function showToast(message, type = 'info', duration = 5000) {
  // Create toast container if it doesn't exist
  let container = document.querySelector('.toast-container');
  if (!container) {
    container = document.createElement('div');
    container.className = 'toast-container';
    document.body.appendChild(container);
  }

  // Icon mapping
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  };

  // Create new toast
  const toast = document.createElement('div');
  toast.className = `toast ${type}`;
  toast.innerHTML = `
    <div class="toast-icon">${icons[type] || icons.info}</div>
    <div class="toast-content">${message}</div>
    <button class="toast-close" onclick="removeToast(this.parentElement)">×</button>
    <div class="toast-progress"></div>
  `;

  // Add to container
  container.appendChild(toast);

  // Auto remove after duration
  setTimeout(() => {
    removeToast(toast);
  }, duration);
}

// Function to remove toast with animation
function removeToast(toast) {
  if (!toast || !toast.parentElement) return;

  toast.style.animation = 'slideOutRight 0.3s ease-out forwards';
  setTimeout(() => {
    if (toast.parentElement) {
      toast.remove();
    }
  }, 300);
}

// Format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format date relative to now
function formatRelativeTime(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days !== 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

// Copy text to clipboard
function copyToClipboard(text) {
  return navigator.clipboard.writeText(text).then(() => {
    showToast('Copied to clipboard!', 'success', 2000);
  }).catch(err => {
    console.error('Failed to copy: ', err);
    showToast('Failed to copy to clipboard', 'error');
  });
}

// Debounce function for search/input handlers
function debounce(func, wait, immediate) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

// Loading state management
function setLoadingState(element, loading = true) {
  if (loading) {
    element.disabled = true;
    element.dataset.originalText = element.textContent;
    element.innerHTML = '<div class="spinner"></div> Loading...';
  } else {
    element.disabled = false;
    element.textContent = element.dataset.originalText || 'Submit';
  }
}

// Auto-refresh functionality for dashboard
function startAutoRefresh(interval = 30000) {
  if (window.location.pathname === '/') {
    setInterval(() => {
      // Only refresh if no modals or forms are active
      if (!document.querySelector('.modal:not(.hidden)') && 
          !document.querySelector('form:focus-within')) {
        window.location.reload();
      }
    }, interval);
  }
}

// Initialize auto-refresh if on dashboard
document.addEventListener('DOMContentLoaded', function() {
  if (window.location.pathname === '/') {
    // Auto-refresh every 5 minutes
    startAutoRefresh(300000);
  }
});

// Image preview functionality
function previewImageFile(file, previewElement) {
  if (file && file.type.startsWith('image/')) {
    const reader = new FileReader();
    reader.onload = function(e) {
      previewElement.src = e.target.result;
      previewElement.style.display = 'block';
    };
    reader.readAsDataURL(file);
  } else {
    previewElement.style.display = 'none';
  }
}

// Form validation helpers
function validateForm(formElement) {
  const requiredFields = formElement.querySelectorAll('[required]');
  let isValid = true;
  
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      field.classList.add('error');
      isValid = false;
    } else {
      field.classList.remove('error');
    }
  });
  
  return isValid;
}

// Enhanced error handling
window.addEventListener('error', function(e) {
  console.error('Global error:', e.error);
  showToast('An unexpected error occurred', 'error');
});

window.addEventListener('unhandledrejection', function(e) {
  console.error('Unhandled promise rejection:', e.reason);
  showToast('An unexpected error occurred', 'error');
});

// Export functions for use in other scripts
window.AIImageGenerator = {
  showToast,
  formatFileSize,
  formatRelativeTime,
  copyToClipboard,
  debounce,
  setLoadingState,
  previewImageFile,
  validateForm
};
