const OpenAI = require('openai');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async generateImage(prompt, referenceImagePath = null) {
    try {
      console.log('🎨 Generating image with prompt:', prompt);
      
      const requestBody = {
        model: 'gpt-image-1',
        prompt: prompt,
        n: 1,
        size: '1024x1024'
      };

      console.log('📋 Request body:', JSON.stringify(requestBody, null, 2));

      // If reference image is provided, include it in the request
      if (referenceImagePath && await fs.pathExists(referenceImagePath)) {
        // Note: For multimodal input, we might need to use a different approach
        // This is a placeholder for the actual implementation
        console.log('📸 Using reference image:', referenceImagePath);
        // In a real implementation, you might need to encode the image and send it
      }

      const response = await this.client.images.generate(requestBody);
      
      if (response.data && response.data.length > 0) {
        const imageData = response.data[0];
        const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
        const filepath = path.join(__dirname, '..', 'generated-images', filename);
        
        // Decode base64 and save image
        const buffer = Buffer.from(imageData.b64_json, 'base64');
        await fs.writeFile(filepath, buffer);
        
        console.log('✅ Image generated and saved:', filename);
        
        return {
          success: true,
          filename: filename,
          filepath: filepath,
          usage: response.usage || null
        };
      } else {
        throw new Error('No image data received from OpenAI');
      }
    } catch (error) {
      console.error('❌ OpenAI generation error:', error);
      throw error;
    }
  }

  async generateImageFromPromptData(promptData) {
    try {
      // Combine text prompt with reference image information
      let enhancedPrompt = promptData.text;
      
      if (promptData.referenceImage) {
        const referenceImagePath = path.join(__dirname, '..', 'uploads', promptData.referenceImage);
        enhancedPrompt = `Use this reference image as inspiration: ${promptData.referenceImage} — ${promptData.text}`;
        
        return await this.generateImage(enhancedPrompt, referenceImagePath);
      } else {
        return await this.generateImage(enhancedPrompt);
      }
    } catch (error) {
      console.error('❌ Error generating image from prompt data:', error);
      throw error;
    }
  }
}

module.exports = new OpenAIService();
