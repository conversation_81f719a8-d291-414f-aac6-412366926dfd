const OpenAI = require('openai');
const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class OpenAIService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async generateImage(prompt, referenceImagePath = null) {
    try {
      console.log('🎨 Generating image with prompt:', prompt);

      // If reference image is provided, use the edit endpoint for multimodal input
      if (referenceImagePath && await fs.pathExists(referenceImagePath)) {
        console.log('📸 Using reference image:', referenceImagePath);
        return await this.generateWithReferenceImage(prompt, referenceImagePath);
      }

      // Standard text-only generation
      const requestBody = {
        model: 'gpt-image-1',
        prompt: prompt,
        n: 1,
        size: process.env.IMAGE_SIZE || '1024x1024',
        quality: process.env.IMAGE_QUALITY || 'medium',
        moderation: process.env.MODERATION_LEVEL || 'auto',
        output_format: process.env.IMAGE_FORMAT || 'png'
      };

      console.log('📋 Request body:', JSON.stringify(requestBody, null, 2));

      const response = await this.client.images.generate(requestBody);
      
      if (response.data && response.data.length > 0) {
        const imageData = response.data[0];
        const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
        const filepath = path.join(__dirname, '..', 'generated-images', filename);
        
        // Decode base64 and save image
        const buffer = Buffer.from(imageData.b64_json, 'base64');
        await fs.writeFile(filepath, buffer);
        
        console.log('✅ Image generated and saved:', filename);
        
        return {
          success: true,
          filename: filename,
          filepath: filepath,
          usage: response.usage || null
        };
      } else {
        throw new Error('No image data received from OpenAI');
      }
    } catch (error) {
      console.error('❌ OpenAI generation error:', error);
      throw error;
    }
  }

  async generateWithReferenceImage(prompt, referenceImagePath) {
    try {
      console.log('🖼️ Generating image with reference:', referenceImagePath);

      // For now, let's use an enhanced text prompt that describes the reference image
      // This is a fallback approach until we confirm the exact multimodal API format
      const enhancedPrompt = `Create an image inspired by the reference image style and composition. ${prompt}. Use similar visual elements, color palette, and artistic style as the reference image while incorporating the new content described.`;

      console.log('📝 Enhanced prompt with reference context:', enhancedPrompt);

      // Use standard generation with enhanced prompt
      // TODO: Implement proper multimodal input when API format is confirmed
      const response = await this.client.images.generate({
        model: 'gpt-image-1',
        prompt: enhancedPrompt,
        n: 1,
        size: process.env.IMAGE_SIZE || '1024x1024',
        quality: process.env.IMAGE_QUALITY || 'medium',
        moderation: process.env.MODERATION_LEVEL || 'auto',
        output_format: process.env.IMAGE_FORMAT || 'png'
      });

      if (response.data && response.data.length > 0) {
        const imageData = response.data[0];
        const filename = `generated_${uuidv4()}.${process.env.IMAGE_FORMAT || 'png'}`;
        const filepath = path.join(__dirname, '..', 'generated-images', filename);

        // Decode base64 and save image
        const buffer = Buffer.from(imageData.b64_json, 'base64');
        await fs.writeFile(filepath, buffer);

        console.log('✅ Image generated with reference and saved:', filename);

        return {
          success: true,
          filename: filename,
          filepath: filepath,
          usage: response.usage || null,
          usedReferenceImage: true
        };
      } else {
        throw new Error('No image data received from OpenAI');
      }
    } catch (error) {
      console.error('❌ Error generating image with reference:', error);

      // Fallback: try text-only generation with enhanced prompt
      console.log('🔄 Falling back to text-only generation...');
      const fallbackPrompt = `Create an image inspired by the uploaded reference image: ${prompt}`;
      return await this.generateImage(fallbackPrompt);
    }
  }

  async generateImageFromPromptData(promptData) {
    try {
      console.log('🎯 Generating image from prompt data:', {
        text: promptData.text,
        hasReferenceImage: !!promptData.referenceImage,
        referenceImage: promptData.referenceImage
      });

      let enhancedPrompt = promptData.text;

      if (promptData.referenceImage) {
        const referenceImagePath = path.join(__dirname, '..', 'uploads', promptData.referenceImage);

        // Check if reference image exists
        if (await fs.pathExists(referenceImagePath)) {
          console.log('📸 Reference image found, using multimodal generation');
          return await this.generateImage(enhancedPrompt, referenceImagePath);
        } else {
          console.log('⚠️ Reference image not found, falling back to text-only generation');
          enhancedPrompt = `Create an image inspired by the concept of "${promptData.referenceImage}": ${promptData.text}`;
          return await this.generateImage(enhancedPrompt);
        }
      } else {
        console.log('📝 Text-only generation');
        return await this.generateImage(enhancedPrompt);
      }
    } catch (error) {
      console.error('❌ Error generating image from prompt data:', error);
      throw error;
    }
  }

  // Helper method to get MIME type from file extension
  getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.webp': 'image/webp',
      '.gif': 'image/gif'
    };
    return mimeTypes[ext] || 'image/jpeg';
  }
}

module.exports = new OpenAIService();
